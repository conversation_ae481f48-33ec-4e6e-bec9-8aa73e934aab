package com.example.myapp;

import android.os.AsyncTask;
import android.util.Log;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;

public class CategoryAPI {
    
    // URL của API backend
    private static final String BASE_URL = "http://10.0.2.2:5000/api";
    private static final String TAG = "CategoryAPI";
    
    // Interface để trả kết quả về
    public interface CategoryCallback {
        void onSuccess(List<Category> categories);
        void onError(String error);
    }
    
    // Hàm gọi API để lấy danh sách categories
    public static void getCategories(String userId, CategoryCallback callback) {
        new GetCategoriesTask(callback).execute(userId);
    }
    
    // AsyncTask để gọi API trong background thread
    private static class GetCategoriesTask extends AsyncTask<String, Void, List<Category>> {
        
        private CategoryCallback callback;
        private String errorMessage;
        
        public GetCategoriesTask(CategoryCallback callback) {
            this.callback = callback;
        }
        
        @Override
        protected List<Category> doInBackground(String... params) {
            String userId = params[0];
            List<Category> categories = new ArrayList<>();
            
            try {
                // Tạo URL với userId
                String urlString = BASE_URL + "/tasks?userId=" + userId;
                Log.d(TAG, "Calling API: " + urlString);
                
                // Tạo kết nối HTTP
                URL url = new URL(urlString);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setRequestProperty("Content-Type", "application/json");
                connection.setConnectTimeout(10000); // 10 giây timeout
                connection.setReadTimeout(10000);
                
                // Kiểm tra response code
                int responseCode = connection.getResponseCode();
                Log.d(TAG, "Response Code: " + responseCode);
                
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    // Đọc response
                    BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                    StringBuilder response = new StringBuilder();
                    String line;
                    
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    reader.close();
                    
                    String jsonResponse = response.toString();
                    Log.d(TAG, "Response: " + jsonResponse);
                    
                    // Parse JSON thành List<Category>
                    categories = parseJsonToCategories(jsonResponse);
                    
                } else {
                    errorMessage = "HTTP Error: " + responseCode;
                    Log.e(TAG, errorMessage);
                }
                
                connection.disconnect();
                
            } catch (Exception e) {
                errorMessage = "Network Error: " + e.getMessage();
                Log.e(TAG, "Error calling API", e);
            }
            
            return categories;
        }
        
        @Override
        protected void onPostExecute(List<Category> categories) {
            if (callback != null) {
                if (errorMessage != null) {
                    callback.onError(errorMessage);
                } else {
                    callback.onSuccess(categories);
                }
            }
        }
    }
    
    // Hàm chuyển JSON thành List<Category>
    private static List<Category> parseJsonToCategories(String jsonString) {
        List<Category> categories = new ArrayList<>();
        
        try {
            JSONArray jsonArray = new JSONArray(jsonString);
            
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                
                Category category = new Category();
                category.setId(jsonObject.optString("_id"));
                category.setName(jsonObject.optString("name"));
                category.setDescription(jsonObject.optString("description"));
                category.setColor(jsonObject.optString("color"));
                category.setIcon(jsonObject.optString("icon"));
                category.setUserId(jsonObject.optString("userId"));
                category.setCreatedAt(jsonObject.optString("createdAt"));
                category.setUpdatedAt(jsonObject.optString("updatedAt"));
                
                categories.add(category);
                
                Log.d(TAG, "Parsed category: " + category.toString());
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error parsing JSON", e);
        }
        
        return categories;
    }
}
