package com.example.myapp;

import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import java.util.List;

public class TestCategoryActivity extends AppCompatActivity implements CategoryManager.CategoryManagerListener {
    
    private static final String TAG = "TestCategoryActivity";
    
    // Views
    private Button btnLoadCategories;
    private TextView tvResult;
    private TextView tvStatus;
    
    // Manager
    private CategoryManager categoryManager;
    
    // User ID để test (thay bằng ID thật của bạn)
    private static final String TEST_USER_ID = "685ba33d4ed50aba7252bb4d";
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_test_category);
        
        // Khởi tạo views
        initViews();
        
        // Khởi tạo CategoryManager
        categoryManager = new CategoryManager(this);
        categoryManager.setListener(this);
        
        // Setup click listeners
        setupClickListeners();
        
        Log.d(TAG, "TestCategoryActivity created");
    }
    
    private void initViews() {
        btnLoadCategories = findViewById(R.id.btnLoadCategories);
        tvResult = findViewById(R.id.tvResult);
        tvStatus = findViewById(R.id.tvStatus);
        
        // Set initial text
        tvStatus.setText("Sẵn sàng test API");
        tvResult.setText("Chưa có dữ liệu");
    }
    
    private void setupClickListeners() {
        btnLoadCategories.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                loadCategoriesFromAPI();
            }
        });
    }
    
    private void loadCategoriesFromAPI() {
        Log.d(TAG, "Button clicked - Loading categories...");
        
        // Hiển thị trạng thái loading
        tvStatus.setText("Đang tải categories...");
        tvResult.setText("Đang gọi API...");
        btnLoadCategories.setEnabled(false);
        
        // Gọi API thông qua CategoryManager
        categoryManager.loadCategories(TEST_USER_ID);
    }
    
    // Implement CategoryManagerListener
    @Override
    public void onCategoriesLoaded(List<Category> categories) {
        Log.d(TAG, "Categories loaded successfully: " + categories.size());
        
        // Update UI
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                tvStatus.setText("Tải thành công! Có " + categories.size() + " categories");
                
                // Hiển thị danh sách categories
                StringBuilder result = new StringBuilder();
                result.append("=== DANH SÁCH CATEGORIES ===\n\n");
                
                if (categories.isEmpty()) {
                    result.append("Không có categories nào");
                } else {
                    for (int i = 0; i < categories.size(); i++) {
                        Category cat = categories.get(i);
                        result.append("Category ").append(i + 1).append(":\n");
                        result.append("- ID: ").append(cat.getId()).append("\n");
                        result.append("- Tên: ").append(cat.getName()).append("\n");
                        result.append("- Mô tả: ").append(cat.getDescription()).append("\n");
                        result.append("- Màu: ").append(cat.getColor()).append("\n");
                        result.append("- Icon: ").append(cat.getIcon()).append("\n");
                        result.append("- UserID: ").append(cat.getUserId()).append("\n");
                        result.append("\n");
                    }
                }
                
                tvResult.setText(result.toString());
                btnLoadCategories.setEnabled(true);
                
                // In ra log để debug
                categoryManager.printAllCategories();
            }
        });
    }
    
    @Override
    public void onCategoriesError(String error) {
        Log.e(TAG, "Error loading categories: " + error);
        
        // Update UI
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                tvStatus.setText("Lỗi: " + error);
                tvResult.setText("Không thể tải categories.\n\nLỗi: " + error + "\n\nKiểm tra:\n" +
                        "1. Backend có chạy không?\n" +
                        "2. URL có đúng không?\n" +
                        "3. Mạng có kết nối không?");
                btnLoadCategories.setEnabled(true);
                
                Toast.makeText(TestCategoryActivity.this, "Lỗi: " + error, Toast.LENGTH_LONG).show();
            }
        });
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "TestCategoryActivity destroyed");
    }
}
