package com.example.myapp.services;

import android.os.AsyncTask;
import android.util.Log;

import com.example.myapp.models.Task;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * Class chuyên xử lý API liên quan đến Tasks
 */
public class TaskAPI {
    
    private static final String TAG = "TaskAPI";
    // Thay IP này nếu dùng thiết bị thật
    private static final String BASE_URL = "http://********:5000/api";
    
    /**
     * Interface để trả kết quả về TodoFragment
     */
    public interface TaskCallback {
        void onSuccess(List<Task> tasks);
        void onError(String errorMessage);
    }
    
    /**
     * Gọi API lấy danh sách tasks
     * @param userId ID của user
     * @param callback Interface để trả kết quả
     */
    public static void getTasks(String userId, TaskCallback callback) {
        new GetTasksTask(callback).execute(userId);
    }
    
    /**
     * AsyncTask gọi API trong background
     */
    private static class GetTasksTask extends AsyncTask<String, Void, List<Task>> {
        
        private TaskCallback callback;
        private String errorMessage;
        
        public GetTasksTask(TaskCallback callback) {
            this.callback = callback;
        }
        
        @Override
        protected List<Task> doInBackground(String... params) {
            String userId = params[0];
            List<Task> tasks = new ArrayList<>();
            
            try {
                // Tạo URL với userId
                String urlString = BASE_URL + "/tasks?userId=" + userId;
                Log.d(TAG, "Gọi API: " + urlString);
                
                // Tạo HTTP connection
                URL url = new URL(urlString);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setRequestProperty("Content-Type", "application/json");
                connection.setConnectTimeout(10000);
                connection.setReadTimeout(10000);
                
                // Kiểm tra response
                int responseCode = connection.getResponseCode();
                Log.d(TAG, "Response Code: " + responseCode);
                
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    // Đọc response
                    BufferedReader reader = new BufferedReader(
                        new InputStreamReader(connection.getInputStream())
                    );
                    StringBuilder response = new StringBuilder();
                    String line;
                    
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    reader.close();
                    
                    String jsonResponse = response.toString();
                    Log.d(TAG, "Response: " + jsonResponse);
                    
                    // Parse JSON
                    tasks = parseJsonToTasks(jsonResponse);
                    
                } else {
                    errorMessage = "Lỗi HTTP: " + responseCode;
                }
                
                connection.disconnect();
                
            } catch (Exception e) {
                errorMessage = "Lỗi kết nối: " + e.getMessage();
                Log.e(TAG, "Lỗi API", e);
            }
            
            return tasks;
        }
        
        @Override
        protected void onPostExecute(List<Task> tasks) {
            if (callback != null) {
                if (errorMessage != null) {
                    callback.onError(errorMessage);
                } else {
                    callback.onSuccess(tasks);
                }
            }
        }
    }
    
    /**
     * Parse JSON response thành List<Task>
     * @param jsonString JSON string từ server
     * @return List<Task>
     */
    private static List<Task> parseJsonToTasks(String jsonString) {
        List<Task> tasks = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault());
        
        try {
            JSONObject jsonResponse = new JSONObject(jsonString);
            JSONArray tasksArray = jsonResponse.getJSONArray("tasks");
            
            for (int i = 0; i < tasksArray.length(); i++) {
                JSONObject taskJson = tasksArray.getJSONObject(i);
                
                Task task = new Task();
                
                // Parse thông tin cơ bản
                task.setId(taskJson.optString("_id"));
                task.setTitle(taskJson.optString("title"));
                task.setDescription(taskJson.optString("description"));
                task.setCompleted(taskJson.optBoolean("isCompleted", false));
                task.setPriority(taskJson.optString("priority", "medium"));
                task.setUserId(taskJson.optString("userId"));
                
                // Parse categoryId - có thể là string hoặc object
                if (taskJson.has("categoryId")) {
                    Object categoryIdObj = taskJson.get("categoryId");
                    if (categoryIdObj instanceof String) {
                        // Nếu là string
                        task.setCategoryId((String) categoryIdObj);
                    } else if (categoryIdObj instanceof JSONObject) {
                        // Nếu là object, lấy _id
                        JSONObject categoryObj = (JSONObject) categoryIdObj;
                        task.setCategoryId(categoryObj.optString("_id"));
                    }
                }
                
                // Parse dates
                try {
                    if (taskJson.has("startDate") && !taskJson.isNull("startDate")) {
                        Date startDate = dateFormat.parse(taskJson.getString("startDate"));
                        task.setStartDate(startDate);
                    }
                    
                    if (taskJson.has("deadline") && !taskJson.isNull("deadline")) {
                        Date deadline = dateFormat.parse(taskJson.getString("deadline"));
                        task.setDeadline(deadline);
                    }
                    
                    if (taskJson.has("createdAt") && !taskJson.isNull("createdAt")) {
                        Date createdAt = dateFormat.parse(taskJson.getString("createdAt"));
                        task.setCreatedAt(createdAt);
                    }
                    
                    if (taskJson.has("updatedAt") && !taskJson.isNull("updatedAt")) {
                        Date updatedAt = dateFormat.parse(taskJson.getString("updatedAt"));
                        task.setUpdatedAt(updatedAt);
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Lỗi parse date cho task: " + task.getTitle(), e);
                }
                
                tasks.add(task);
                Log.d(TAG, "Parsed task: " + task.getTitle());
            }
            
            Log.d(TAG, "Tổng số tasks parsed: " + tasks.size());
            
        } catch (Exception e) {
            Log.e(TAG, "Lỗi parse JSON tasks", e);
        }
        
        return tasks;
    }
}