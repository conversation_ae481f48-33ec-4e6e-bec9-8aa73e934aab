package com.example.myapp.fragments;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.example.myapp.R;

public class NavBarFragment extends Fragment {

    private LinearLayout navTasks, navCalendar, navProfile, navCategories, navSettings;
    private ImageButton buttonCloseNav;

    private OnNavItemClickListener listener;

    public interface OnNavItemClickListener {
        void onTasksClick();
        void onCalendarClick();
        void onProfileClick();
        void onCategoriesClick();
        void onSettingsClick();
        void onCloseNavBar();
    }

    public NavBarFragment() {
        // Required empty public constructor
    }

    public static NavBarFragment newInstance() {
        return new NavBarFragment();
    }

    public void setOnNavItemClickListener(OnNavItemClickListener listener) {
        this.listener = listener;
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_navbar, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        initViews(view);
        setupClickListeners();
    }

    private void initViews(View view) {
        navTasks = view.findViewById(R.id.navTasks);
        navCalendar = view.findViewById(R.id.navCalendar);
        navProfile = view.findViewById(R.id.navProfile);
        navCategories = view.findViewById(R.id.navCategories);
        navSettings = view.findViewById(R.id.navSettings);
        buttonCloseNav = view.findViewById(R.id.buttonCloseNav);
    }

    private void setupClickListeners() {
        navTasks.setOnClickListener(v -> {
            if (listener != null) {
                listener.onTasksClick();
            }
        });

        navCalendar.setOnClickListener(v -> {
            if (listener != null) {
                listener.onCalendarClick();
            }
        });

        navProfile.setOnClickListener(v -> {
            if (listener != null) {
                listener.onProfileClick();
            }
        });

        navCategories.setOnClickListener(v -> {
            if (listener != null) {
                listener.onCategoriesClick();
            }
        });

        navSettings.setOnClickListener(v -> {
            if (listener != null) {
                listener.onSettingsClick();
            }
        });

        buttonCloseNav.setOnClickListener(v -> {
            if (listener != null) {
                listener.onCloseNavBar();
            }
        });
    }
}
