package com.example.myapp;

public class Category {
    private String _id;
    private String name;
    private String description;
    private String color;
    private String icon;
    private String userId;
    private String createdAt;
    private String updatedAt;

    // Constructor rỗng (bắt buộc)
    public Category() {}

    // Constructor đầy đủ
    public Category(String _id, String name, String description, String color, String icon, String userId) {
        this._id = _id;
        this.name = name;
        this.description = description;
        this.color = color;
        this.icon = icon;
        this.userId = userId;
    }

    // Getters và Setters
    public String getId() {
        return _id;
    }

    public void setId(String _id) {
        this._id = _id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "Category{" +
                "_id='" + _id + '\'' +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", color='" + color + '\'' +
                ", icon='" + icon + '\'' +
                ", userId='" + userId + '\'' +
                '}';
    }
}
