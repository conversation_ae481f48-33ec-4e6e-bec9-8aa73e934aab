package com.example.myapp;

import android.content.Context;
import android.util.Log;
import android.widget.Toast;

import java.util.ArrayList;
import java.util.List;

public class CategoryManager {
    
    private static final String TAG = "CategoryManager";
    private Context context;
    private List<Category> categories;
    private CategoryManagerListener listener;
    
    // Interface để thông báo khi có thay đổi
    public interface CategoryManagerListener {
        void onCategoriesLoaded(List<Category> categories);
        void onCategoriesError(String error);
    }
    
    public CategoryManager(Context context) {
        this.context = context;
        this.categories = new ArrayList<>();
    }
    
    public void setListener(CategoryManagerListener listener) {
        this.listener = listener;
    }
    
    // Hàm chính để load categories từ API
    public void loadCategories(String userId) {
        Log.d(TAG, "Loading categories for userId: " + userId);
        
        // Gọi API
        CategoryAPI.getCategories(userId, new CategoryAPI.CategoryCallback() {
            @Override
            public void onSuccess(List<Category> categoriesFromAPI) {
                Log.d(TAG, "Successfully loaded " + categoriesFromAPI.size() + " categories");
                
                // Lưu vào biến local
                categories.clear();
                categories.addAll(categoriesFromAPI);
                
                // Thông báo cho listener
                if (listener != null) {
                    listener.onCategoriesLoaded(categories);
                }
                
                // Hiển thị thông báo thành công
                if (context != null) {
                    Toast.makeText(context, "Đã tải " + categories.size() + " categories", Toast.LENGTH_SHORT).show();
                }
            }
            
            @Override
            public void onError(String error) {
                Log.e(TAG, "Error loading categories: " + error);
                
                // Thông báo cho listener
                if (listener != null) {
                    listener.onCategoriesError(error);
                }
                
                // Hiển thị thông báo lỗi
                if (context != null) {
                    Toast.makeText(context, "Lỗi tải categories: " + error, Toast.LENGTH_LONG).show();
                }
            }
        });
    }
    
    // Getter cho danh sách categories
    public List<Category> getCategories() {
        return new ArrayList<>(categories); // Trả về copy để tránh modify trực tiếp
    }
    
    // Kiểm tra có categories không
    public boolean hasCategories() {
        return categories != null && !categories.isEmpty();
    }
    
    // Lấy category theo ID
    public Category getCategoryById(String categoryId) {
        if (categories != null) {
            for (Category category : categories) {
                if (category.getId() != null && category.getId().equals(categoryId)) {
                    return category;
                }
            }
        }
        return null;
    }
    
    // Lấy category theo tên
    public Category getCategoryByName(String categoryName) {
        if (categories != null) {
            for (Category category : categories) {
                if (category.getName() != null && category.getName().equals(categoryName)) {
                    return category;
                }
            }
        }
        return null;
    }
    
    // In ra log tất cả categories (để debug)
    public void printAllCategories() {
        Log.d(TAG, "=== ALL CATEGORIES ===");
        if (categories != null && !categories.isEmpty()) {
            for (int i = 0; i < categories.size(); i++) {
                Category cat = categories.get(i);
                Log.d(TAG, "Category " + i + ": " + cat.toString());
            }
        } else {
            Log.d(TAG, "No categories found");
        }
        Log.d(TAG, "=== END CATEGORIES ===");
    }
    
    // Clear tất cả categories
    public void clearCategories() {
        if (categories != null) {
            categories.clear();
        }
    }
    
    // Thêm category mới (nếu cần)
    public void addCategory(Category category) {
        if (categories != null && category != null) {
            categories.add(category);
        }
    }
}
