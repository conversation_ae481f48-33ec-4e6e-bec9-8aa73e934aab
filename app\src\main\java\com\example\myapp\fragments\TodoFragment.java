package com.example.myapp.fragments;

import com.example.myapp.services.CategoryAPI;
import android.widget.Toast;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.example.myapp.services.TaskAPI;

import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.example.myapp.R;
import com.example.myapp.activities.AddTaskActivity;
import com.example.myapp.adapters.TaskAdapter;
import com.example.myapp.adapters.CategoryAdapter;
import com.example.myapp.models.Task;
import com.example.myapp.models.Category;
import com.example.myapp.services.ApiService;

import java.util.ArrayList;
import java.util.List;

public class TodoFragment extends Fragment implements TaskAdapter.OnTaskClickListener, CategoryAdapter.OnCategoryClickListener, NavBarFragment.OnNavItemClickListener {

    // Views
    private DrawerLayout drawerLayout;
    private RecyclerView recyclerViewTasks, recyclerViewCategories;
    private TextView textEmptyState;
    private ImageButton buttonMenu, buttonSearch;
    private Button buttonAddCategory;
    private FloatingActionButton fabAddTask;
    private LinearLayout navTasks, navCalendar, navProfile;

    // Data
    private TaskAdapter taskAdapter;
    private CategoryAdapter categoryAdapter;
    private List<Task> allTasks, filteredTasks;
    private List<Category> categories;
    private Category selectedCategory;

    // Services
    private ApiService apiService;

    // Navigation
    private OnNavigationListener navigationListener;

    public interface OnNavigationListener {
        void onNavigateToCalendar();
        void onNavigateToProfile();
    }

    public TodoFragment() {
        // Required empty public constructor
    }

    public static TodoFragment newInstance() {
        return new TodoFragment();
    }

    public void setOnNavigationListener(OnNavigationListener listener) {
        this.navigationListener = listener;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Initialize data
        allTasks = new ArrayList<>();
        filteredTasks = new ArrayList<>();
        categories = new ArrayList<>();

        // Initialize API service
        apiService = new ApiService();

        // Initialize with safe defaults
        initializeDefaultData();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_todo, container, false);
    }


    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
    super.onViewCreated(view, savedInstanceState);

    initViews(view);
    setupRecyclerViews();
    setupClickListeners();
    setupNavBar();
    
    // Gọi API để load dữ liệu thật
    loadCategories();  // Load categories trước
    loadTasks();       // Load tasks sau
}

    private void initViews(View view) {
        drawerLayout = view.findViewById(R.id.drawerLayout);
        recyclerViewTasks = view.findViewById(R.id.recyclerViewTasks);
        recyclerViewCategories = view.findViewById(R.id.recyclerViewCategories);
        textEmptyState = view.findViewById(R.id.textEmptyState);
        buttonMenu = view.findViewById(R.id.buttonMenu);
        buttonSearch = view.findViewById(R.id.buttonSearch);
        buttonAddCategory = view.findViewById(R.id.buttonAddCategory);
        fabAddTask = view.findViewById(R.id.fabAddTask);
        navTasks = view.findViewById(R.id.navTasks);
        navCalendar = view.findViewById(R.id.navCalendar);
        navProfile = view.findViewById(R.id.navProfile);
    }

    private void setupRecyclerViews() {
        // Setup tasks RecyclerView
        taskAdapter = new TaskAdapter(filteredTasks, this);
        recyclerViewTasks.setLayoutManager(new LinearLayoutManager(getContext()));
        recyclerViewTasks.setAdapter(taskAdapter);

        // Setup categories RecyclerView
        categoryAdapter = new CategoryAdapter(categories, this);
        recyclerViewCategories.setLayoutManager(new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false));
        recyclerViewCategories.setAdapter(categoryAdapter);
    }

    private void setupClickListeners() {
        buttonMenu.setOnClickListener(v -> {
            View navContainer = getView().findViewById(R.id.navContainer);
            if (drawerLayout.isDrawerOpen(navContainer)) {
                drawerLayout.closeDrawer(navContainer);
            } else {
                drawerLayout.openDrawer(navContainer);
            }
        });

        buttonSearch.setOnClickListener(v -> {
            Toast.makeText(getContext(), "Tìm kiếm nhiệm vụ", Toast.LENGTH_SHORT).show();
            // TODO: Implement search functionality
        });

        buttonAddCategory.setOnClickListener(v -> {
            Toast.makeText(getContext(), "Thêm chủ đề mới", Toast.LENGTH_SHORT).show();
            // TODO: Implement add category functionality
        });

        fabAddTask.setOnClickListener(v -> {
            Intent intent = new Intent(getContext(), AddTaskActivity.class);
            startActivity(intent);
        });

        navTasks.setOnClickListener(v -> {
            // Already on tasks screen
        });

        navCalendar.setOnClickListener(v -> {
            if (navigationListener != null) {
                navigationListener.onNavigateToCalendar();
            }
        });

        navProfile.setOnClickListener(v -> {
            if (navigationListener != null) {
                navigationListener.onNavigateToProfile();
            }
        });
    }

    private void setupNavBar() {
        NavBarFragment navBarFragment = NavBarFragment.newInstance();
        navBarFragment.setOnNavItemClickListener(this);

        FragmentTransaction transaction = getChildFragmentManager().beginTransaction();
        transaction.replace(R.id.navContainer, navBarFragment);
        transaction.commit();
    }

    private void initializeDefaultData() {
        // Add "Tất cả" category
        Category allCategory = new Category("Tất cả", "Tất cả nhiệm vụ", "#9E9E9E", "all");
        allCategory.setId("all");
        categories.add(allCategory);
        selectedCategory = allCategory;

        android.util.Log.d("TodoFragment", "Initialized default data. Categories: " + categories.size());
    }

    private void loadSampleData() {
        // Add sample categories
        Category workCategory = new Category("Công việc", "Các nhiệm vụ liên quan đến công việc", "#2196F3", "work");
        workCategory.setId("1");
        categories.add(workCategory);

        Category personalCategory = new Category("Cá nhân", "Các nhiệm vụ cá nhân", "#4CAF50", "personal");
        personalCategory.setId("2");
        categories.add(personalCategory);

        Category studyCategory = new Category("Học tập", "Các nhiệm vụ học tập", "#FF9800", "study");
        studyCategory.setId("3");
        categories.add(studyCategory);

        // Add sample tasks
        Task task1 = new Task("Hoàn thành báo cáo", "Viết báo cáo tháng cho dự án", "1", new java.util.Date(), "high");
        Task task2 = new Task("Họp team", "Họp review dự án với team", "1", new java.util.Date(), "medium");
        Task task3 = new Task("Học Android", "Học về Fragment và RecyclerView", "3", new java.util.Date(), "low");

        allTasks.add(task1);
        allTasks.add(task2);
        allTasks.add(task3);

        filterTasks();
    }

    private void loadCategories() {
    // Gọi API thật thay vì dùng dữ liệu mẫu
    String userId = "685ba33d4ed50aba7252bb4d"; // Thay bằng userId thật của bạn
    

    CategoryAPI.getCategories(userId, new CategoryAPI.CategoryCallback() {
        @Override
        public void onSuccess(List<Category> categoriesFromAPI) {

            // Chạy trên UI thread
            if (getActivity() != null) {
                getActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        // Giữ lại category "Tất cả" ở đầu
                        Category allCategory = null;
                        if (!categories.isEmpty()) {
                            allCategory = categories.get(0); // "Tất cả"
                        }
                        
                        // Xóa tất cả categories cũ
                        categories.clear();
                        
                        // Thêm lại "Tất cả" nếu có
                        if (allCategory != null) {
                            categories.add(allCategory);
                        }
                        
                        // Thêm categories từ API
                        categories.addAll(categoriesFromAPI);
                        
                        // Cập nhật giao diện
                        if (categoryAdapter != null) {
                            categoryAdapter.notifyDataSetChanged();
                        }
                        
                        // Hiển thị thông báo thành công
                        Toast.makeText(getContext(), 
                            "Đã tải " + categoriesFromAPI.size() + " categories từ server", 
                            Toast.LENGTH_SHORT).show();
                    }
                });
            }
        }
        
        @Override
        public void onError(String errorMessage) {

            if (getActivity() != null) {
                getActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        // Hiển thị thông báo lỗi
                        Toast.makeText(getContext(), 
                            "Lỗi tải categories: " + errorMessage, 
                            Toast.LENGTH_LONG).show();
                        
                        // Vẫn hiển thị dữ liệu mẫu nếu API lỗi
                        loadSampleData();
                    }
                });
            }
        }
    });
}

   private void loadTasks() {
    String userId = "685ba33d4ed50aba7252bb4d";
    
    Log.d("TodoFragment", "Bắt đầu load tasks từ API...");
    
    TaskAPI.getTasks(userId, new TaskAPI.TaskCallback() {
        @Override
        public void onSuccess(List<Task> tasksFromAPI) {
            Log.d("TodoFragment", "API thành công! Nhận được " + tasksFromAPI.size() + " tasks");
            
            if (getActivity() != null) {
                getActivity().runOnUiThread(() -> {
                    // Cập nhật danh sách tasks
                    allTasks.clear();
                    allTasks.addAll(tasksFromAPI);
                    
                    // Lọc tasks theo category đã chọn
                    filterTasks();
                    
                    // Cập nhật UI
                    updateEmptyState();
                    
                    Toast.makeText(getContext(), 
                        "Đã tải " + tasksFromAPI.size() + " tasks từ server", 
                        Toast.LENGTH_SHORT).show();
                });
            }
        }
        
        @Override
        public void onError(String errorMessage) {
            Log.e("TodoFragment", "API lỗi: " + errorMessage);
            
            if (getActivity() != null) {
                getActivity().runOnUiThread(() -> {
                    Toast.makeText(getContext(), 
                        "Lỗi tải tasks: " + errorMessage, 
                        Toast.LENGTH_LONG).show();
                    
                    // Fallback về dữ liệu mẫu
                    loadSampleData();
                });
            }
        }
    });
}

/**
 * Load tất cả tasks của user (không filter theo category)
 */
private void loadAllTasks() {
    String userId = "685ba33d4ed50aba7252bb4d";

    Log.d("TodoFragment", "Load tất cả tasks...");

    TaskAPI.getTasks(userId, new TaskAPI.TaskCallback() {
        @Override
        public void onSuccess(List<Task> tasksFromAPI) {
            Log.d("TodoFragment", "Load all tasks thành công! " + tasksFromAPI.size() + " tasks");

            if (getActivity() != null) {
                getActivity().runOnUiThread(() -> {
                    // Cập nhật danh sách tasks
                    allTasks.clear();
                    allTasks.addAll(tasksFromAPI);

                    // Hiển thị tất cả tasks
                    filteredTasks.clear();
                    filteredTasks.addAll(allTasks);

                    // Cập nhật UI
                    if (taskAdapter != null) {
                        taskAdapter.notifyDataSetChanged();
                    }
                    updateEmptyState();
                });
            }
        }

        @Override
        public void onError(String errorMessage) {
            Log.e("TodoFragment", "Lỗi load all tasks: " + errorMessage);

            if (getActivity() != null) {
                getActivity().runOnUiThread(() -> {
                    Toast.makeText(getContext(),
                        "Lỗi tải tasks: " + errorMessage,
                        Toast.LENGTH_LONG).show();
                });
            }
        }
    });
}

/**
 * Load tasks theo categoryId cụ thể
 * @param categoryId ID của category
 */
private void loadTasksByCategory(String categoryId) {
    String userId = "685ba33d4ed50aba7252bb4d";

    Log.d("TodoFragment", "Load tasks theo categoryId: " + categoryId);

    TaskAPI.getTasksByCategory(userId, categoryId, new TaskAPI.TaskCallback() {
        @Override
        public void onSuccess(List<Task> tasksFromAPI) {
            Log.d("TodoFragment", "Load tasks by category thành công! " + tasksFromAPI.size() + " tasks");

            if (getActivity() != null) {
                getActivity().runOnUiThread(() -> {
                    // Cập nhật danh sách tasks đã filter
                    filteredTasks.clear();
                    filteredTasks.addAll(tasksFromAPI);

                    // Cập nhật UI
                    if (taskAdapter != null) {
                        taskAdapter.notifyDataSetChanged();
                    }
                    updateEmptyState();
                });
            }
        }

        @Override
        public void onError(String errorMessage) {
            Log.e("TodoFragment", "Lỗi load tasks by category: " + errorMessage);

            if (getActivity() != null) {
                getActivity().runOnUiThread(() -> {
                    Toast.makeText(getContext(),
                        "Lỗi tải tasks: " + errorMessage,
                        Toast.LENGTH_LONG).show();
                });
            }
        }
    });
}

    private void filterTasks() {
        filteredTasks.clear();

        // Debug log
        android.util.Log.d("TodoFragment", "filterTasks called. selectedCategory: " +
            (selectedCategory != null ? selectedCategory.getName() : "null"));

        if (selectedCategory == null || selectedCategory.getId() == null) {
            android.util.Log.d("TodoFragment", "Using all tasks (category null or id null)");
            filteredTasks.addAll(allTasks);
        } else if ("all".equals(selectedCategory.getId())) {
            android.util.Log.d("TodoFragment", "Using all tasks (category = all)");
            filteredTasks.addAll(allTasks);
        } else {
            android.util.Log.d("TodoFragment", "Filtering by category: " + selectedCategory.getId());
            for (Task task : allTasks) {
                if (task.getCategoryId() != null && task.getCategoryId().equals(selectedCategory.getId())) {
                    filteredTasks.add(task);
                }
            }
        }

        if (taskAdapter != null) {
            taskAdapter.notifyDataSetChanged();
        }
        updateEmptyState();
    }

    private void updateEmptyState() {
        // Check if views are initialized
        if (recyclerViewTasks == null || textEmptyState == null) {
            return;
        }

        if (filteredTasks.isEmpty()) {
            recyclerViewTasks.setVisibility(View.GONE);
            textEmptyState.setVisibility(View.VISIBLE);
        } else {
            recyclerViewTasks.setVisibility(View.VISIBLE);
            textEmptyState.setVisibility(View.GONE);
        }
    }

    // TaskAdapter.OnTaskClickListener implementation
    @Override
    public void onTaskToggle(Task task, int position) {
        // TODO: Update task on server
        // apiService.updateTask(task, new ApiService.TaskCallback() { ... });
        String message = task.isCompleted() ? "Nhiệm vụ đã hoàn thành!" : "Nhiệm vụ chưa hoàn thành!";
        Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onTaskClick(Task task, int position) {
        Toast.makeText(getContext(), "Xem chi tiết: " + task.getTitle(), Toast.LENGTH_SHORT).show();
        // TODO: Navigate to task detail screen
    }

    @Override
    public void onTaskDelete(Task task, int position) {
        // TODO: Delete task on server
        // apiService.deleteTask(task.getId(), new ApiService.SimpleCallback() { ... });
        allTasks.remove(task);
        filteredTasks.remove(position);
        taskAdapter.notifyItemRemoved(position);
        updateEmptyState();
        Toast.makeText(getContext(), "Đã xóa nhiệm vụ!", Toast.LENGTH_SHORT).show();
    }

    // CategoryAdapter.OnCategoryClickListener implementation
    @Override
    public void onCategoryClick(Category category, int position) {
        if (category == null) {
            Toast.makeText(getContext(), "Lỗi: Category null", Toast.LENGTH_SHORT).show();
            return;
        }

        selectedCategory = category;
        String categoryName = category.getName() != null ? category.getName() : "Unknown";

        // Nếu chọn "Tất cả" thì load tất cả tasks
        if ("all".equals(category.getId())) {
            loadAllTasks();
            Toast.makeText(getContext(), "Hiển thị tất cả tasks", Toast.LENGTH_SHORT).show();
        } else {
            // Nếu chọn category cụ thể thì gọi API với categoryId
            loadTasksByCategory(category.getId());
            Toast.makeText(getContext(), "Lọc theo: " + categoryName, Toast.LENGTH_SHORT).show();
        }
    }

    // NavBarFragment.OnNavItemClickListener implementation
    @Override
    public void onTasksClick() {
        // Already on tasks screen
    }

    @Override
    public void onCalendarClick() {
        if (navigationListener != null) {
            navigationListener.onNavigateToCalendar();
        }
    }

    @Override
    public void onProfileClick() {
        if (navigationListener != null) {
            navigationListener.onNavigateToProfile();
        }
    }

    @Override
    public void onCategoriesClick() {
        Toast.makeText(getContext(), "Quản lý chủ đề", Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onSettingsClick() {
        Toast.makeText(getContext(), "Cài đặt", Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onCloseNavBar() {
        if (drawerLayout.isDrawerOpen(getView().findViewById(R.id.navContainer))) {
            drawerLayout.closeDrawer(getView().findViewById(R.id.navContainer));
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (apiService != null) {
            apiService.shutdown();
        }
    }
}
