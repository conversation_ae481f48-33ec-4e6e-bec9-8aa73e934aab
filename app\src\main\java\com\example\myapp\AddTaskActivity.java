package com.example.myapp.activities;

import android.app.DatePickerDialog;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.RadioGroup;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.app.AppCompatActivity;

import com.google.android.material.textfield.TextInputEditText;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class AddTaskActivity extends AppCompatActivity {

    private TextInputEditText editTextTitle, editTextDescription;
    private Spinner spinnerCategory;
    private RadioGroup radioGroupPriority;
    private TextView textStartDate, textDeadline;
    private ImageView imagePreview;
    private Button buttonSelectStartDate, buttonSelectDeadline, buttonSelectImage, buttonRemoveImage;
    private ImageButton buttonBack;
    private Button buttonSave;

    private Date startDate, deadline;
    private Uri selectedImageUri;
    private List<Category> categories;
    private SimpleDateFormat dateFormat;

    private ActivityResultLauncher<Intent> imagePickerLauncher;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_add_task);

        initViews();
        setupDateFormat();
        setupImagePicker();
        setupClickListeners();
        loadCategories();
        setDefaultValues();
    }

    private void initViews() {
        editTextTitle = findViewById(R.id.editTextTitle);
        editTextDescription = findViewById(R.id.editTextDescription);
        spinnerCategory = findViewById(R.id.spinnerCategory);
        radioGroupPriority = findViewById(R.id.radioGroupPriority);
        textStartDate = findViewById(R.id.textStartDate);
        textDeadline = findViewById(R.id.textDeadline);
        imagePreview = findViewById(R.id.imagePreview);
        buttonSelectStartDate = findViewById(R.id.buttonSelectStartDate);
        buttonSelectDeadline = findViewById(R.id.buttonSelectDeadline);
        buttonSelectImage = findViewById(R.id.buttonSelectImage);
        buttonRemoveImage = findViewById(R.id.buttonRemoveImage);
        buttonBack = findViewById(R.id.buttonBack);
        buttonSave = findViewById(R.id.buttonSave);
    }

    private void setupDateFormat() {
        dateFormat = new SimpleDateFormat("dd/MM/yyyy", Locale.getDefault());
    }

    private void setupImagePicker() {
        imagePickerLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    if (result.getResultCode() == RESULT_OK && result.getData() != null) {
                        selectedImageUri = result.getData().getData();
                        imagePreview.setImageURI(selectedImageUri);
                        buttonRemoveImage.setVisibility(View.VISIBLE);
                    }
                }
        );
    }

    private void setupClickListeners() {
        buttonBack.setOnClickListener(v -> finish());

        buttonSave.setOnClickListener(v -> saveTask());

        buttonSelectStartDate.setOnClickListener(v -> showDatePicker(true));

        buttonSelectDeadline.setOnClickListener(v -> showDatePicker(false));

        buttonSelectImage.setOnClickListener(v -> selectImage());

        buttonRemoveImage.setOnClickListener(v -> removeImage());
    }

    private void loadCategories() {

        categories = new ArrayList<>();
        categories.add(new Category("Công việc", "Các nhiệm vụ liên quan đến công việc", "#2196F3", "work"));
        categories.add(new Category("Cá nhân", "Các nhiệm vụ cá nhân", "#4CAF50", "personal"));
        categories.add(new Category("Học tập", "Các nhiệm vụ học tập", "#FF9800", "study"));

        ArrayAdapter<Category> adapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, categories);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerCategory.setAdapter(adapter);
    }

    private void setDefaultValues() {
        startDate = new Date();
        textStartDate.setText(dateFormat.format(startDate));

        // Set default deadline to tomorrow
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, 1);
        deadline = cal.getTime();
        textDeadline.setText(dateFormat.format(deadline));
    }

    private void showDatePicker(boolean isStartDate) {
        Calendar calendar = Calendar.getInstance();
        if (isStartDate && startDate != null) {
            calendar.setTime(startDate);
        } else if (!isStartDate && deadline != null) {
            calendar.setTime(deadline);
        }

        DatePickerDialog datePickerDialog = new DatePickerDialog(
                this,
                (view, year, month, dayOfMonth) -> {
                    Calendar selectedDate = Calendar.getInstance();
                    selectedDate.set(year, month, dayOfMonth);
                    
                    if (isStartDate) {
                        startDate = selectedDate.getTime();
                        textStartDate.setText(dateFormat.format(startDate));
                    } else {
                        deadline = selectedDate.getTime();
                        textDeadline.setText(dateFormat.format(deadline));
                    }
                },
                calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH)
        );

        if (!isStartDate) {
            // Set minimum date for deadline to today
            datePickerDialog.getDatePicker().setMinDate(System.currentTimeMillis());
        }

        datePickerDialog.show();
    }

    private void selectImage() {
        Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
        intent.setType("image/*");
        imagePickerLauncher.launch(intent);
    }

    private void removeImage() {
        selectedImageUri = null;
        imagePreview.setImageResource(android.R.drawable.ic_menu_camera);
        buttonRemoveImage.setVisibility(View.GONE);
    }

    private void saveTask() {
        String title = editTextTitle.getText().toString().trim();
        String description = editTextDescription.getText().toString().trim();

        // Validation
        if (TextUtils.isEmpty(title)) {
            editTextTitle.setError("Vui lòng nhập tiêu đề");
            editTextTitle.requestFocus();
            return;
        }

        if (spinnerCategory.getSelectedItem() == null) {
            Toast.makeText(this, "Vui lòng chọn chủ đề", Toast.LENGTH_SHORT).show();
            return;
        }

        if (deadline == null) {
            Toast.makeText(this, "Vui lòng chọn hạn chót", Toast.LENGTH_SHORT).show();
            return;
        }

        // Get selected priority
        String priority = "medium";
        int selectedPriorityId = radioGroupPriority.getCheckedRadioButtonId();
        if (selectedPriorityId == R.id.radioLow) {
            priority = "low";
        } else if (selectedPriorityId == R.id.radioHigh) {
            priority = "high";
        }

        // Get selected category
        Category selectedCategory = (Category) spinnerCategory.getSelectedItem();

        // Create task object
        Task newTask = new Task(title, description, selectedCategory.getId(), deadline, priority);
        newTask.setStartDate(startDate);
        
        if (selectedImageUri != null) {
            newTask.setImagePath(selectedImageUri.toString());
        }

        // TODO: Save task to API
        
        // Return result
        Intent resultIntent = new Intent();
        resultIntent.putExtra("task_title", title);
        resultIntent.putExtra("task_description", description);
        resultIntent.putExtra("task_priority", priority);
        setResult(RESULT_OK, resultIntent);
        
        Toast.makeText(this, "Nhiệm vụ đã được tạo!", Toast.LENGTH_SHORT).show();
        finish();
    }
}
