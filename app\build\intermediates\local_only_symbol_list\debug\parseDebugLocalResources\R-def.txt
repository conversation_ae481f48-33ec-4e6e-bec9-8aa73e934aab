R_DEF: Internal format may change without notice
local
color black
color white
drawable ic_launcher_background
drawable ic_launcher_foreground
id btnLoadCategories
id buttonAddCategory
id buttonBack
id buttonCancel
id buttonClearSearch
id buttonCloseSearch
id buttonMenu
id buttonRemoveImage
id buttonSave
id buttonSearch
id buttonSelectDeadline
id buttonSelectImage
id buttonSelectStartDate
id buttonSettings
id buttonTaskMenu
id buttonToday
id calendarView
id categoryColorDot
id categoryColorIndicator
id checkboxCompleted
id colorBlue
id colorGreen
id colorOrange
id colorPurple
id colorRed
id colorTeal
id drawerLayout
id editTextCategoryDescription
id editTextCategoryName
id editTextDescription
id editTextSearch
id editTextTitle
id fabAddTask
id fragment_container
id imagePreview
id imageProfile
id layoutEmptySearch
id layoutNoResults
id main
id menuEditProfile
id menuHelp
id menuLogout
id menuNotifications
id menuPrivacy
id navCalendar
id navContainer
id navProfile
id navTasks
id nav_calendar
id nav_categories
id nav_profile
id nav_settings
id nav_tasks
id priorityIndicator
id radioGroupPriority
id radioGroupSearchType
id radioHigh
id radioLow
id radioMedium
id radioSearchAll
id radioSearchCategories
id radioSearchTasks
id recyclerViewCalendarTasks
id recyclerViewCategories
id recyclerViewSearchResults
id recyclerViewTasks
id selectedColorPreview
id spinnerCategory
id textCategoryDescription
id textCategoryName
id textCategoryType
id textCompletedTasks
id textDeadline
id textEmptyState
id textNoTasksForDate
id textOverdue
id textPendingTasks
id textSearchResultsHeader
id textSelectedColor
id textSelectedDate
id textStartDate
id textTaskDeadline
id textTaskDescription
id textTaskTitle
id textTaskType
id textUserEmail
id textUserName
id tvResult
id tvStatus
layout activity_add_task
layout activity_main
layout activity_test_category
layout dialog_add_category
layout dialog_search
layout fragment_calendar
layout fragment_navbar
layout fragment_profile
layout fragment_todo
layout item_category
layout item_search_category
layout item_search_task
layout item_task
mipmap ic_launcher
mipmap ic_launcher_round
string app_name
style Base.Theme.MyApp
style Theme.MyApp
xml backup_rules
xml data_extraction_rules
