package com.example.myapp.fragments;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.example.myapp.R;

public class ProfileFragment extends Fragment {

    private TextView textUserName, textUserEmail, textTasksCompleted, textTasksPending;
    private ImageButton buttonMenu, buttonEditProfile;
    private LinearLayout navTasks, navCalendar, navProfile;

    private OnNavigationClickListener navigationListener;

    public interface OnNavigationClickListener {
        void onTasksClick();
        void onCalendarClick();
        void onProfileClick();
        void onMenuClick();
    }

    public ProfileFragment() {
        // Required empty public constructor
    }

    public static ProfileFragment newInstance() {
        return new ProfileFragment();
    }

    public void setOnNavigationClickListener(OnNavigationClickListener listener) {
        this.navigationListener = listener;
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_profile, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        initViews(view);
        setupClickListeners();
        loadUserData();
    }

    private void initViews(View view) {
        textUserName = view.findViewById(R.id.textUserName);
        textUserEmail = view.findViewById(R.id.textUserEmail);
        textTasksCompleted = view.findViewById(R.id.textTasksCompleted);
        textTasksPending = view.findViewById(R.id.textTasksPending);
        buttonMenu = view.findViewById(R.id.buttonMenu);
        buttonEditProfile = view.findViewById(R.id.buttonEditProfile);
        navTasks = view.findViewById(R.id.navTasks);
        navCalendar = view.findViewById(R.id.navCalendar);
        navProfile = view.findViewById(R.id.navProfile);
    }

    private void setupClickListeners() {
        navTasks.setOnClickListener(v -> {
            if (navigationListener != null) {
                navigationListener.onTasksClick();
            }
        });

        navCalendar.setOnClickListener(v -> {
            if (navigationListener != null) {
                navigationListener.onCalendarClick();
            }
        });

        navProfile.setOnClickListener(v -> {
            // Already on profile screen
        });
    }

    private void loadUserData() {
        // Load sample user data
        textUserName.setText("Người dùng");
        textUserEmail.setText("<EMAIL>");
        textTasksCompleted.setText("12");
        textTasksPending.setText("5");
    }
}
