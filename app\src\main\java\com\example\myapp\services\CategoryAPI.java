package main.java.com.example.myapp.services;

import android.os.AsyncTask;
import android.util.Log;

import com.example.myapp.models.Category;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;

/**
 * Class chuyên xử lý API liên quan đến Categories
 * Sử dụng HttpURLConnection (có sẵn trong Android)
 */
public class CategoryAPI {
    
    private static final String TAG = "CategoryAPI";
    // Thay đổi IP này cho phù hợp:
    // - ******** nếu dùng Android Emulator
    // - 192.168.x.x nếu dùng thiết bị thật (IP máy tính)
    private static final String BASE_URL = "http://********:5000/api";
    
    /**
     * Interface để trả kết quả về cho Fragment
     */
    public interface CategoryCallback {
        void onSuccess(List<Category> categories);  // Thành công
        void onError(String errorMessage);          // Lỗi
    }
    
    /**
     * Hàm chính để gọi API lấy categories
     * @param userId ID của user
     * @param callback Interface để trả kết quả
     */
    public static void getCategories(String userId, CategoryCallback callback) {
        // Chạy API call trong background thread
        new GetCategoriesTask(callback).execute(userId);
    }
    
    /**
     * AsyncTask để gọi API trong background (không block UI)
     */
    private static class GetCategoriesTask extends AsyncTask<String, Void, List<Category>> {
        
        private CategoryCallback callback;
        private String errorMessage;
        
        public GetCategoriesTask(CategoryCallback callback) {
            this.callback = callback;
        }
        
        @Override
        protected List<Category> doInBackground(String... params) {
            String userId = params[0];
            List<Category> categories = new ArrayList<>();
            
            try {
                // 1. Tạo URL với userId
                String urlString = BASE_URL + "/categories?userId=" + userId;
                Log.d(TAG, "Gọi API: " + urlString);
                
                // 2. Tạo HTTP connection
                URL url = new URL(urlString);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setRequestProperty("Content-Type", "application/json");
                connection.setConnectTimeout(10000); // 10 giây timeout
                connection.setReadTimeout(10000);
                
                // 3. Kiểm tra response code
                int responseCode = connection.getResponseCode();
                Log.d(TAG, "Response Code: " + responseCode);
                
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    // 4. Đọc response từ server
                    BufferedReader reader = new BufferedReader(
                        new InputStreamReader(connection.getInputStream())
                    );
                    StringBuilder response = new StringBuilder();
                    String line;
                    
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    reader.close();
                    
                    String jsonResponse = response.toString();
                    Log.d(TAG, "Response JSON: " + jsonResponse);
                    
                    // 5. Parse JSON thành List<Category>
                    categories = parseJsonToCategories(jsonResponse);
                    
                } else {
                    errorMessage = "Lỗi HTTP: " + responseCode;
                    Log.e(TAG, errorMessage);
                }
                
                connection.disconnect();
                
            } catch (Exception e) {
                errorMessage = "Lỗi kết nối: " + e.getMessage();
                Log.e(TAG, "Lỗi gọi API", e);
            }
            
            return categories;
        }
        
        @Override
        protected void onPostExecute(List<Category> categories) {
            // Chạy trên UI thread - trả kết quả về Fragment
            if (callback != null) {
                if (errorMessage != null) {
                    callback.onError(errorMessage);
                } else {
                    callback.onSuccess(categories);
                }
            }
        }
    }
    
    /**
     * Chuyển JSON response thành List<Category>
     * @param jsonString JSON string từ server
     * @return List<Category>
     */
    private static List<Category> parseJsonToCategories(String jsonString) {
        List<Category> categories = new ArrayList<>();
        
        try {
            JSONArray jsonArray = new JSONArray(jsonString);
            
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                
                // Tạo Category object từ JSON
                Category category = new Category();
                category.setId(jsonObject.optString("_id"));
                category.setName(jsonObject.optString("name"));
                category.setDescription(jsonObject.optString("description"));
                category.setColor(jsonObject.optString("color"));
                category.setIcon(jsonObject.optString("icon"));
                category.setUserId(jsonObject.optString("userId"));
                
                categories.add(category);
                
                Log.d(TAG, "Parsed category: " + category.getName());
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Lỗi parse JSON", e);
        }
        
        return categories;
    }
}