package com.example.myapp.services;

import android.os.AsyncTask;
import android.util.Log;

import com.example.myapp.models.Category;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;

/**
 * Class chuyên xử lý API categories
 */
public class CategoryAPI {
    
    private static final String TAG = "CategoryAPI";
    // Thay IP này nếu dùng thiết bị thật
    private static final String BASE_URL = "http://********:5000/api";
    
    /**
     * Interface để trả kết quả về TodoFragment
     */
    public interface CategoryCallback {
        void onSuccess(List<Category> categories);
        void onError(String errorMessage);
    }
    
    /**
     * Gọi API lấy categories
     */
    public static void getCategories(String userId, CategoryCallback callback) {
        new GetCategoriesTask(callback).execute(userId);
    }
    
    /**
     * AsyncTask gọi API trong background
     */
    private static class GetCategoriesTask extends AsyncTask<String, Void, List<Category>> {
        
        private CategoryCallback callback;
        private String errorMessage;
        
        public GetCategoriesTask(CategoryCallback callback) {
            this.callback = callback;
        }
        
        @Override
        protected List<Category> doInBackground(String... params) {
            String userId = params[0];
            List<Category> categories = new ArrayList<>();
            
            try {
                // Tạo URL
                String urlString = BASE_URL + "/categories?userId=" + userId;
                Log.d(TAG, "Gọi API: " + urlString);
                
                // Tạo HTTP connection
                URL url = new URL(urlString);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setRequestProperty("Content-Type", "application/json");
                connection.setConnectTimeout(10000);
                connection.setReadTimeout(10000);
                
                // Kiểm tra response
                int responseCode = connection.getResponseCode();
                Log.d(TAG, "Response Code: " + responseCode);
                
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    // Đọc response
                    BufferedReader reader = new BufferedReader(
                        new InputStreamReader(connection.getInputStream())
                    );
                    StringBuilder response = new StringBuilder();
                    String line;
                    
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    reader.close();
                    
                    String jsonResponse = response.toString();
                    Log.d(TAG, "Response: " + jsonResponse);
                    
                    // Parse JSON
                    categories = parseJsonToCategories(jsonResponse);
                    
                } else {
                    errorMessage = "Lỗi HTTP: " + responseCode;
                }
                
                connection.disconnect();
                
            } catch (Exception e) {
                errorMessage = "Lỗi kết nối: " + e.getMessage();
                Log.e(TAG, "Lỗi API", e);
            }
            
            return categories;
        }
        
        @Override
        protected void onPostExecute(List<Category> categories) {
            if (callback != null) {
                if (errorMessage != null) {
                    callback.onError(errorMessage);
                } else {
                    callback.onSuccess(categories);
                }
            }
        }
    }
    
    /**
     * Parse JSON thành List<Category>
     */
    private static List<Category> parseJsonToCategories(String jsonString) {
        List<Category> categories = new ArrayList<>();
        
        try {
            JSONArray jsonArray = new JSONArray(jsonString);
            
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                
                Category category = new Category();
                category.setId(jsonObject.optString("_id"));
                category.setName(jsonObject.optString("name"));
                category.setDescription(jsonObject.optString("description"));
                category.setColor(jsonObject.optString("color"));
                category.setIcon(jsonObject.optString("icon"));
                category.setUserId(jsonObject.optString("userId"));
                
                categories.add(category);
                Log.d(TAG, "Parsed: " + category.getName());
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Lỗi parse JSON", e);
        }
        
        return categories;
    }
}