<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="#f5f5f5">

    <!-- Header -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="TEST API CATEGORIES"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="#333333"
        android:gravity="center"
        android:layout_marginBottom="20dp" />

    <!-- Button để gọi API -->
    <Button
        android:id="@+id/btnLoadCategories"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="dowload api"
        android:textSize="16sp"
        android:textStyle="bold"
        android:background="#4CAF50"
        android:textColor="#FFFFFF"
        android:padding="12dp"
        android:layout_marginBottom="16dp" />

    <!-- Trạng thái -->
    <TextView
        android:id="@+id/tvStatus"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="San sang test api"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="#2196F3"
        android:background="#E3F2FD"
        android:padding="12dp"
        android:layout_marginBottom="16dp" />

    <!-- Kết quả -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="#FFFFFF"
        android:padding="8dp">

        <TextView
            android:id="@+id/tvResult"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Chưa có dữ liệu"
            android:textSize="14sp"
            android:textColor="#333333"
            android:fontFamily="monospace"
            android:padding="8dp" />

    </ScrollView>

    <!-- Footer info -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Kiểm tra Logcat để xem chi tiết"
        android:textSize="12sp"
        android:textColor="#666666"
        android:gravity="center"
        android:layout_marginTop="8dp" />

</LinearLayout>
