package com.example.myapp.activities;

import android.app.DatePickerDialog;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.RadioGroup;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.app.AppCompatActivity;

import com.google.android.material.textfield.TextInputEditText;
import com.example.myapp.R;
import com.example.myapp.models.Task;
import com.example.myapp.models.Category;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class AddTaskActivity extends AppCompatActivity {

    // Views
    private TextInputEditText editTaskTitle, editTaskDescription;
    private Spinner spinnerCategory;
    private RadioGroup radioGroupPriority;
    private TextView textSelectedDate;
    private Button buttonSelectDate, buttonSaveTask, buttonCancel;
    private ImageButton buttonBack, buttonAddImage;
    private ImageView imagePreview;

    // Data
    private Date selectedDate;
    private String selectedImagePath;
    private List<Category> categories;
    private SimpleDateFormat dateFormat;

    // Image picker
    private ActivityResultLauncher<Intent> imagePickerLauncher;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_add_task);

        initViews();
        setupImagePicker();
        setupCategories();
        setupClickListeners();
        
        dateFormat = new SimpleDateFormat("dd/MM/yyyy", Locale.getDefault());
    }

    private void initViews() {
        editTaskTitle = findViewById(R.id.editTaskTitle);
        editTaskDescription = findViewById(R.id.editTaskDescription);
        spinnerCategory = findViewById(R.id.spinnerCategory);
        radioGroupPriority = findViewById(R.id.radioGroupPriority);
        textSelectedDate = findViewById(R.id.textSelectedDate);
        buttonSelectDate = findViewById(R.id.buttonSelectDate);
        buttonSaveTask = findViewById(R.id.buttonSaveTask);
        buttonCancel = findViewById(R.id.buttonCancel);
        buttonBack = findViewById(R.id.buttonBack);
        buttonAddImage = findViewById(R.id.buttonAddImage);
        imagePreview = findViewById(R.id.imagePreview);
    }

    private void setupImagePicker() {
        imagePickerLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    if (result.getResultCode() == RESULT_OK && result.getData() != null) {
                        Uri imageUri = result.getData().getData();
                        if (imageUri != null) {
                            selectedImagePath = imageUri.toString();
                            imagePreview.setImageURI(imageUri);
                            imagePreview.setVisibility(View.VISIBLE);
                        }
                    }
                }
        );
    }

    private void setupCategories() {
        categories = new ArrayList<>();
        
        // Add sample categories
        Category workCategory = new Category("Công việc", "Các nhiệm vụ liên quan đến công việc", "#2196F3", "work");
        workCategory.setId("1");
        categories.add(workCategory);

        Category personalCategory = new Category("Cá nhân", "Các nhiệm vụ cá nhân", "#4CAF50", "personal");
        personalCategory.setId("2");
        categories.add(personalCategory);

        Category studyCategory = new Category("Học tập", "Các nhiệm vụ học tập", "#FF9800", "study");
        studyCategory.setId("3");
        categories.add(studyCategory);

        // Setup spinner
        List<String> categoryNames = new ArrayList<>();
        for (Category category : categories) {
            categoryNames.add(category.getName());
        }

        ArrayAdapter<String> adapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, categoryNames);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerCategory.setAdapter(adapter);
    }

    private void setupClickListeners() {
        buttonBack.setOnClickListener(v -> finish());

        buttonCancel.setOnClickListener(v -> finish());

        buttonSelectDate.setOnClickListener(v -> showDatePicker());

        buttonAddImage.setOnClickListener(v -> openImagePicker());

        buttonSaveTask.setOnClickListener(v -> saveTask());
    }

    private void showDatePicker() {
        Calendar calendar = Calendar.getInstance();
        
        DatePickerDialog datePickerDialog = new DatePickerDialog(
                this,
                (view, year, month, dayOfMonth) -> {
                    calendar.set(year, month, dayOfMonth);
                    selectedDate = calendar.getTime();
                    textSelectedDate.setText(dateFormat.format(selectedDate));
                },
                calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH)
        );
        
        datePickerDialog.show();
    }

    private void openImagePicker() {
        Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
        intent.setType("image/*");
        imagePickerLauncher.launch(intent);
    }

    private void saveTask() {
        String title = editTaskTitle.getText().toString().trim();
        String description = editTaskDescription.getText().toString().trim();

        // Validate input
        if (TextUtils.isEmpty(title)) {
            editTaskTitle.setError("Vui lòng nhập tiêu đề");
            editTaskTitle.requestFocus();
            return;
        }

        // Get selected category
        int selectedCategoryPosition = spinnerCategory.getSelectedItemPosition();
        String categoryId = categories.get(selectedCategoryPosition).getId();

        // Get selected priority
        String priority = "medium"; // default
        int selectedPriorityId = radioGroupPriority.getCheckedRadioButtonId();
        if (selectedPriorityId == R.id.radioPriorityHigh) {
            priority = "high";
        } else if (selectedPriorityId == R.id.radioPriorityMedium) {
            priority = "medium";
        } else if (selectedPriorityId == R.id.radioPriorityLow) {
            priority = "low";
        }

        // Create task
        Task newTask = new Task(title, description, categoryId, selectedDate, priority);
        if (selectedImagePath != null) {
            newTask.setImagePath(selectedImagePath);
        }

        // TODO: Save task to database/API
        Toast.makeText(this, "Nhiệm vụ đã được tạo!", Toast.LENGTH_SHORT).show();
        
        // Return to previous screen
        finish();
    }
}
